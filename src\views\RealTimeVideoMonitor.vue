<template>
    <div class="index-container">
        <TopNav />
        <div class="main-content">
            <SideNav />
            <div class="content">
                <ContentTabs />
                <div class="content-container">
                    <!-- 内容区域 -->
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import TopNav from '../components/TopNav.vue';
import SideNav from '@/components/SideNav.vue';
import ContentTabs from '@/components/ContentTabs.vue';
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.index-container {
    width: 100%;
    height: 100%;
    background: url(../assets/img/background.png);
    background-size: cover;
    background-repeat: no-repeat;
}

.main-content {
    display: flex;
    height: calc(100vh - 60px);
}

.content {
    width: calc(100vw - 350px);
    height: calc(100vh - 60px);
    overflow: hidden;
}

.content-container {
    width: 100%;
    height: calc(100% - 140px);
    overflow: hidden;
    background-color: #0C192C;
    opacity: 0.86;
}
</style>