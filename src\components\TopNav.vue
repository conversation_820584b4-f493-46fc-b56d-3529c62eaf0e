<template>
    <div class="top-nav">
        <div class="nav-container">
            <div class="nav-left">
                <button class="nav-button" :class="{ active: currentTab === 'public' }"
                    @click="handleTabClick('public')">
                    公共场所在线监测
                </button>
                <button class="nav-button" :class="{ active: currentTab === 'radiation' }"
                    @click="handleTabClick('radiation')">
                    放射卫生在线监测
                </button>
            </div>
            <h1 class="nav-title" @click="handleTabClick('Index')">
                漳平市卫生监督管理系统
            </h1>
            <div class="nav-right">
                <button class="nav-button" :class="{ active: currentTab === 'occupational' }"
                    @click="handleTabClick('occupational')">
                    职业病危害因素在线监测
                </button>
                <button class="nav-button" :class="{ active: currentTab === 'video' }" @click="handleTabClick('video')">
                    实时视频监控
                </button>
                <button class="nav-button" :class="{ active: currentTab === 'support' }"
                    @click="handleTabClick('support')">
                    支撑应用管理
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

// 导航配置：统一管理导航项和路由的映射关系
const navConfig = {
    'Index': 'Index',
    'public': 'PublicPlaceMonitor',
    'radiation': 'RadiationHealthMonitor',
    'occupational': 'OccupationalHazardMonitor',
    'video': 'RealTimeVideoMonitor',
    'support': 'SupportAppManagement'
};

// 支撑应用管理相关的子页面路由
const supportSubRoutes = ['SupportAppManagement', 'AddDevice'];

// 反向映射：从路由名称到tab标识
const routeToTabMap = Object.fromEntries(
    Object.entries(navConfig).map(([tab, routeName]) => [routeName, tab])
);

// 为支撑应用管理的子页面添加映射
supportSubRoutes.forEach(routeName => {
    routeToTabMap[routeName] = 'support';
});

// 根据当前路由计算当前选中的tab
const currentTab = computed(() => {
    return routeToTabMap[route.name] || '';
});

const handleTabClick = (tab) => {
    // 获取对应的路由名称
    const routeName = navConfig[tab];

    if (routeName) {
        // 执行路由跳转
        router.push({ name: routeName })
            .catch(error => {
                console.error('路由跳转失败:', error);
                // 可以在这里添加错误处理逻辑，如显示提示信息
            });
    } else {
        console.warn(`未找到标签 "${tab}" 对应的路由`);
    }
};

</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.top-nav {
    background-color: $secondary-color;
    @include flex-center;
    height: 60px;
    background-size: cover;
    background-repeat: no-repeat;
}

.nav-container {
    @include flex-center;
    position: relative;
    width: 100%;
    max-width: 1920px;
    padding: 0 $spacing-md;
}

.nav-left,
.nav-right {
    display: flex;
    align-items: center;
    gap: $spacing-xl;
}

.nav-left {
    margin-left: 14vw;
    margin-right: auto;
}

.nav-right {
    margin-left: auto;
    margin-right: 3vw;
}

.nav-button {
    padding: $spacing-sm $spacing-md;
    color: $text-white;
    background: transparent;
    border: none;
    border-radius: $border-radius-md;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    font-size: 18px;
    text-align: center;
    font-style: normal;
    text-transform: none;

    &.active {
        background-color: $primary-color;
    }

    &:hover:not(.active) {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

.nav-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    color: $text-white;
    margin: 0;
    width: 331px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    font-size: 30px;
    line-height: 50px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    cursor: pointer;
}
</style>