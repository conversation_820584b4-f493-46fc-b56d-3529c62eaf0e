<template>
    <div class="index-container">
        <TopNav />
        <div class="main-content">
            <SideNav @nav-click="handleNavClick" />
            <div class="content">
                <ContentTabs ref="contentTabsRef" />
                <div class="content-container">
                    <!-- 温度/噪音数据栏 -->
                    <div class="data-header">
                        <span class="data-title">温度/噪音数据</span>
                    </div>

                    <!-- 图表展示区域 -->
                    <div class="charts-container">
                        <div class="charts-column">
                            <div class="chart-item">
                                <img src="../assets/img/echart1.png" alt="图表1" class="chart-image" />
                            </div>
                            <div class="chart-item">
                                <img src="../assets/img/echart2.png" alt="图表2" class="chart-image" />
                            </div>
                        </div>
                        <div class="charts-column">
                            <div class="chart-item">
                                <img src="../assets/img/echart3.png" alt="图表3" class="chart-image" />
                            </div>
                            <div class="chart-item">
                                <img src="../assets/img/echart4.png" alt="图表4" class="chart-image" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import TopNav from '../components/TopNav.vue';
import SideNav from '@/components/SideNav.vue';
import ContentTabs from '@/components/ContentTabs.vue';

// 组件引用
const contentTabsRef = ref(null)

// 处理侧边栏导航点击
const handleNavClick = ({ index, item }) => {
    // 切换ContentTabs中的机构信息
    if (contentTabsRef.value) {
        contentTabsRef.value.switchInstitution(index)
    }
}
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.index-container {
    width: 100%;
    height: 100%;
    background: url(../assets/img/background.png);
    background-size: cover;
    background-repeat: no-repeat;
}

.main-content {
    display: flex;
    height: calc(100vh - 60px);
}

.content {
    width: calc(100vw - 350px);
    height: calc(100vh - 60px);
    overflow: hidden;
}

.content-container {
    width: 100%;
    height: calc(100% - 140px);
    overflow: hidden;
    background-color: #0C192C;
    opacity: 0.86;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

// 温度/噪音数据栏
.data-header {
    width: 100%;
    height: 52px;
    background: linear-gradient(90deg, rgba(28, 72, 160, 0.8) 0%, rgba(11, 35, 85, 0.8) 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    margin-bottom: 20px;
}

.data-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
}

// 图表展示区域
.charts-container {
    flex: 1;
    display: flex;
    gap: 12px;
    height: calc(100% - 72px); // 减去header高度和间距
}

.charts-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.chart-item {
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
}

.chart-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}
</style>