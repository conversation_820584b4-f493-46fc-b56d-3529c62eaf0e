<template>
    <div class="content-tabs">
        <!-- 顶部 Tab 栏 -->
        <div class="tab-bar">
            <!-- 汉堡菜单图标 -->
            <div class="hamburger-menu" @click="toggleSidebar">
                <svg class="hamburger-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
            </div>

            <!-- Tab 项容器 -->
            <div class="tabs-container">
                <div v-for="tab in tabs" :key="tab.id" class="tab-item"
                    :class="{ 'tab-item--active': tab.id === activeTabId }" @click="setActiveTab(tab.id)">
                    <span class="tab-text">{{ tab.name }}</span>
                    <button class="tab-close-btn" @click.stop="closeTab(tab.id)" v-if="tab.closable">
                        <svg class="close-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- 信息展示栏 -->
        <div class="info-bar">
            <div class="info-content">
                <!-- 医疗机构图片 -->
                <div class="institution-avatar">
                    <img :src="institutionInfo.avatar || defaultAvatar" :alt="institutionInfo.name"
                        class="avatar-img" />
                </div>

                <!-- 定级医疗机构标题 -->
                <div class="institution-title">
                    <h3 class="title-text">{{ institutionInfo.title }}</h3>
                </div>

                <!-- 信息项列表 -->
                <div class="info-items">
                    <div style="display: flex; flex-direction: column; gap: 10px; align-items: flex-start;">
                        <div class="info-item">
                            <svg class="info-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M21 10C21 17 12 23 12 23S3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z"
                                    stroke="currentColor" stroke-width="2" />
                                <path
                                    d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z"
                                    stroke="currentColor" stroke-width="2" />
                            </svg>
                            <span class="info-text">所在镇/街道：{{ institutionInfo.district }}</span>
                        </div>

                        <div class="info-item">
                            <svg class="info-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M21 10C21 17 12 23 12 23S3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z"
                                    stroke="currentColor" stroke-width="2" />
                                <path
                                    d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z"
                                    stroke="currentColor" stroke-width="2" />
                            </svg>
                            <span class="info-text">详细地址： {{ institutionInfo.address }}</span>
                        </div>
                    </div>

                    <div class="info-item" style="display: flex; align-items:flex-end;">
                        <svg class="info-icon" style="vertical-align: bottom;" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06579 2.16708 8.43376 2.48353C8.80173 2.79999 9.04207 3.23945 9.10999 3.72C9.23662 4.68007 9.47144 5.62273 9.80999 6.53C9.94454 6.88792 9.97366 7.27691 9.8939 7.65088C9.81415 8.02485 9.62886 8.36811 9.35999 8.64L8.08999 9.91C9.51355 12.4135 11.5865 14.4864 14.09 15.91L15.36 14.64C15.6319 14.3711 15.9751 14.1858 16.3491 14.1061C16.7231 14.0263 17.1121 14.0555 17.47 14.19C18.3773 14.5286 19.3199 14.7634 20.28 14.89C20.7658 14.9585 21.2094 15.2032 21.5265 15.5775C21.8437 15.9518 22.0122 16.4296 22 16.92Z"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <span class="info-text">
                            联系电话：<span class="phone-number">{{ institutionInfo.phone }}</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, defineEmits } from 'vue'
import placeImg from '../assets/img/place.png'

// Emits
const emit = defineEmits(['toggle-sidebar', 'tab-change', 'tab-close'])

// 响应式数据
const activeTabId = ref('video-monitor')

// Tab 数据
const tabs = ref([
    {
        id: 'home',
        name: '首页',
    },
    {
        id: 'video-monitor',
        name: '公共场所在线检测',
        closable: true
    }
])

// 医疗机构信息（预留参数）
const institutionInfo = ref({
    title: '音乐厅',
    avatar: placeImg, // 预留图片URL参数
    name: '医疗机构名称',
    district: '和平镇',
    address: '漳平市和平镇和平村',
    phone: '75802161111'
})

// 默认头像
const defaultAvatar = ''

// 方法
const toggleSidebar = () => {
    emit('toggle-sidebar')
}

const setActiveTab = (tabId) => {
    activeTabId.value = tabId
    emit('tab-change', tabId)
}

const closeTab = (tabId) => {
    const tabIndex = tabs.value.findIndex(tab => tab.id === tabId)
    if (tabIndex > -1) {
        tabs.value.splice(tabIndex, 1)

        // 如果关闭的是当前活跃tab，切换到第一个tab
        if (activeTabId.value === tabId && tabs.value.length > 0) {
            activeTabId.value = tabs.value[0].id
        }

        emit('tab-close', tabId)
    }
}
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;
@use "sass:color";

.content-tabs {
    width: 100%;
}

// Tab 栏样式
.tab-bar {
    width: 100%;
    height: $tab-height;
    background: $bg-gray-100;
    @include flex-between;
    padding: 0 $spacing-md;
    box-shadow: $shadow-sm;
}

.hamburger-menu {
    @include flex-center;
    width: 32px;
    height: 32px;
    cursor: pointer;
    border-radius: $border-radius-sm;
    transition: background-color 0.3s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.05);
    }
}

.hamburger-icon {
    width: 20px;
    height: 20px;
    color: $text-secondary;
}

.tabs-container {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    flex: 1;
    margin-left: $spacing-lg;
}

.tab-item {
    position: relative;
    height: 40px;
    @include flex-center;
    padding: 0 $spacing-md;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: $spacing-sm;

    &--active {
        background: $blue-primary;

        .tab-text {
            color: $text-white;
        }

        .tab-close-btn {
            color: $text-white;
        }
    }

    &:not(.tab-item--active) {
        background: $bg-gray-50;

        .tab-text {
            color: $text-primary;
        }

        .tab-close-btn {
            color: $text-secondary;
        }

        &:hover {
            background: color.adjust($bg-gray-50, $lightness: -5%);
        }
    }
}

.tab-text {
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

.tab-close-btn {
    @include flex-center;
    width: 16px;
    height: 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: $border-radius-sm;
    transition: all 0.3s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.1);
    }
}

.close-icon {
    width: 12px;
    height: 12px;
}

// 信息展示栏样式
.info-bar {
    width: 100%;
    background: $gradient-blue-dark;
    padding: $spacing-md $spacing-lg;
}

.info-content {
    @include flex-center;
    gap: $spacing-lg;
    max-width: 1200px;
    margin: 0 auto;
}

.institution-avatar {
    flex-shrink: 0;
}

.avatar-img {
    width: 120px; // 预留圆形直径参数
    height: 120px;
    border-radius: $border-radius-full;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.institution-title {
    flex-shrink: 0;
    margin-right: $spacing-lg;
}

.title-text {
    color: $text-white;
    margin: 0;
    font-family: MicrosoftYaHei, MicrosoftYaHei;
    font-weight: bold;
    font-size: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
}

.info-items {
    display: flex;
    align-items: center;
    gap: $spacing-xl;
    flex: 1;
}

.info-item {
    @include flex-center;
    gap: $spacing-sm;
}

.info-icon {
    width: 16px; // 预留图标大小参数
    height: 16px;
    color: $text-white;
    flex-shrink: 0;
}

.info-text {
    font-size: 14px;
    color: $text-white;
    white-space: nowrap;
}

.phone-number {
    font-size: 30px; // 突出显示电话号码
    font-weight: bold;

}

// 响应式适配
@media (max-width: 1024px) {
    .info-items {
        gap: $spacing-lg;
    }

    .info-text {
        font-size: 13px;
    }
}

@media (max-width: 768px) {
    .info-content {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-md;
    }

    .info-items {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-sm;
        width: 100%;
    }

    .tabs-container {
        margin-left: $spacing-md;
    }
}
</style>