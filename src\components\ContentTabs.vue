<template>
    <div class="content-tabs">
        <!-- 顶部 Tab 栏 -->
        <div class="tab-bar">
            <!-- 汉堡菜单图标 -->
            <div class="hamburger-menu" @click="toggleSidebar">
                <svg class="hamburger-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
            </div>

            <!-- Tab 项容器 -->
            <div class="tabs-container">
                <div v-for="tab in tabs" :key="tab.id" class="tab-item"
                    :class="{ 'tab-item--active': tab.id === activeTabId }" @click="setActiveTab(tab.id)">
                    <span class="tab-text">{{ tab.name }}</span>
                    <button class="tab-close-btn" @click.stop="closeTab(tab.id)" v-if="tab.closable">
                        <svg class="close-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- 信息展示栏 -->
        <div class="info-bar">
            <div class="info-content">
                <!-- 医疗机构图片 -->
                <div class="institution-avatar">
                    <img :src="institutionInfo.avatar || defaultAvatar" :alt="institutionInfo.name"
                        class="avatar-img" />
                </div>

                <!-- 定级医疗机构标题 -->
                <div class="institution-title">
                    <h3 class="title-text">{{ institutionInfo.title }}</h3>
                </div>

                <!-- 信息项列表 -->
                <div class="info-items">
                    <div style="display: flex; flex-direction: column; gap: 10px; align-items: flex-start;">
                        <div class="info-item">
                            <img src="../assets/img/乡镇地界@2x.png" class="info-icon" />
                            <span class="info-text">所在镇/街道：{{ institutionInfo.district }}</span>
                        </div>

                        <div class="info-item">
                            <img src="../assets/img/详细地址@2x.png" class="info-icon" />
                            <span class="info-text">详细地址： {{ institutionInfo.address }}</span>
                        </div>
                    </div>

                    <div class="info-item" style="display: flex; align-items:flex-end;">
                        <img src="../assets/img/联系电话@2x.png" class="info-icon" />
                        <span class="info-text">
                            联系电话：<span class="phone-number">{{ institutionInfo.phone }}</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, defineEmits, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import placeImg from '../assets/img/place.png'

// Emits
const emit = defineEmits(['toggle-sidebar', 'tab-change', 'tab-close'])

// 路由
const route = useRoute()

// 响应式数据
const activeTabId = ref('monitor')

// 根据路由配置不同页面的数据
const pageConfigs = {
    'PublicPlaceMonitor': {
        tabs: [
            { id: 'home', name: '首页' },
            { id: 'monitor', name: '公共场所在线监测', closable: true }
        ],
        institutions: [
            {
                title: '漳平市蓝方娱乐会所',
                avatar: placeImg,
                name: '漳平市蓝方娱乐会所',
                district: '和平镇',
                address: '漳平市和平镇和平村',
                phone: '75802161111'
            },
            {
                title: '漳平市心满意足养生馆',
                avatar: placeImg,
                name: '漳平市心满意足养生馆',
                district: '溪南镇',
                address: '漳平市溪南镇南柄村',
                phone: '75802162222'
            }
        ]
    },
    'RadiationHealthMonitor': {
        tabs: [
            { id: 'home', name: '首页' },
            { id: 'monitor', name: '放射卫生在线监测', closable: true }
        ],
        institutions: [
            {
                title: '放射医院1',
                avatar: placeImg,
                name: '放射医院1',
                district: '和平镇',
                address: '漳平市和平镇医院路1号',
                phone: '75802163333'
            },
            {
                title: '放射医院2',
                avatar: placeImg,
                name: '放射医院2',
                district: '溪南镇',
                address: '漳平市溪南镇医院路2号',
                phone: '75802164444'
            }
        ]
    },
    'OccupationalHazardMonitor': {
        tabs: [
            { id: 'home', name: '首页' },
            { id: 'monitor', name: '职业卫生', closable: true }
        ],
        institutions: [
            {
                title: '职业卫生公司一',
                avatar: placeImg,
                name: '职业卫生公司一',
                district: '和平镇',
                address: '漳平市和平镇工业路1号',
                phone: '75802165555'
            },
            {
                title: '职业卫生公司二',
                avatar: placeImg,
                name: '职业卫生公司二',
                district: '溪南镇',
                address: '漳平市溪南镇工业路2号',
                phone: '75802166666'
            }
        ]
    },
    'RealTimeVideoMonitor': {
        tabs: [
            { id: 'home', name: '首页' },
            { id: 'monitor', name: '定时视频监控', closable: true }
        ],
        institutions: [
            {
                title: '漳平市和平镇卫生院',
                avatar: placeImg,
                name: '漳平市和平镇卫生院',
                district: '和平镇',
                address: '漳平市和平镇卫生院路1号',
                phone: '75802167777'
            },
            {
                title: '漳平市溪南镇南柄村第一卫生室',
                avatar: placeImg,
                name: '漳平市溪南镇南柄村第一卫生室',
                district: '溪南镇',
                address: '漳平市溪南镇南柄村卫生室路1号',
                phone: '75802168888'
            }
        ]
    }
}

// 当前页面配置
const currentConfig = computed(() => {
    return pageConfigs[route.name] || pageConfigs['PublicPlaceMonitor']
})

// Tab 数据
const tabs = computed(() => currentConfig.value.tabs)

// 当前选中的机构索引
const currentInstitutionIndex = ref(0)

// 医疗机构信息
const institutionInfo = computed(() => {
    const institutions = currentConfig.value.institutions
    return institutions[currentInstitutionIndex.value] || institutions[0]
})

// 默认头像
const defaultAvatar = ''

// 监听路由变化，重置状态
watch(() => route.name, () => {
    currentInstitutionIndex.value = 0
    activeTabId.value = 'monitor'
})

// 方法
const toggleSidebar = () => {
    emit('toggle-sidebar')
}

const setActiveTab = (tabId) => {
    activeTabId.value = tabId
    emit('tab-change', tabId)
}

const closeTab = (tabId) => {
    // 注意：这里不能直接修改computed的tabs，因为它是只读的
    // 如果需要关闭tab功能，需要在父组件中处理
    emit('tab-close', tabId)
}

// 切换机构信息（供外部调用）
const switchInstitution = (index) => {
    if (index >= 0 && index < currentConfig.value.institutions.length) {
        currentInstitutionIndex.value = index
    }
}

// 暴露方法给父组件
defineExpose({
    switchInstitution
})
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;
@use "sass:color";

.content-tabs {
    width: 100%;
}

// Tab 栏样式
.tab-bar {
    width: 100%;
    height: $tab-height;
    background: $bg-gray-100;
    @include flex-between;
    padding: 0 $spacing-md;
    box-shadow: $shadow-sm;
}

.hamburger-menu {
    @include flex-center;
    width: 32px;
    height: 32px;
    cursor: pointer;
    border-radius: $border-radius-sm;
    transition: background-color 0.3s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.05);
    }
}

.hamburger-icon {
    width: 20px;
    height: 20px;
    color: $text-secondary;
}

.tabs-container {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    flex: 1;
    margin-left: $spacing-lg;
}

.tab-item {
    position: relative;
    height: 40px;
    @include flex-center;
    padding: 0 $spacing-md;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: $spacing-sm;

    &--active {
        background: $blue-primary;

        .tab-text {
            color: $text-white;
        }

        .tab-close-btn {
            color: $text-white;
        }
    }

    &:not(.tab-item--active) {
        background: $bg-gray-50;

        .tab-text {
            color: $text-primary;
        }

        .tab-close-btn {
            color: $text-secondary;
        }

        &:hover {
            background: color.adjust($bg-gray-50, $lightness: -5%);
        }
    }
}

.tab-text {
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

.tab-close-btn {
    @include flex-center;
    width: 16px;
    height: 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: $border-radius-sm;
    transition: all 0.3s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.1);
    }
}

.close-icon {
    width: 12px;
    height: 12px;
}

// 信息展示栏样式
.info-bar {
    width: 100%;
    background: $gradient-blue-dark;
    padding: $spacing-md $spacing-lg;
}

.info-content {
    @include flex-center;
    gap: $spacing-lg;
    max-width: 1200px;
    margin: 0 auto;
}

.institution-avatar {
    flex-shrink: 0;
}

.avatar-img {
    width: 120px; // 预留圆形直径参数
    height: 120px;
    border-radius: $border-radius-full;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.institution-title {
    flex-shrink: 0;
    margin-right: $spacing-lg;
}

.title-text {
    color: $text-white;
    margin: 0;
    font-family: MicrosoftYaHei, MicrosoftYaHei;
    font-weight: bold;
    font-size: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
}

.info-items {
    display: flex;
    align-items: center;
    gap: $spacing-xl;
    flex: 1;
}

.info-item {
    @include flex-center;
    gap: $spacing-sm;
}

.info-icon {
    width: 16px; // 预留图标大小参数
    height: 16px;
    color: $text-white;
    flex-shrink: 0;
}

.info-text {
    font-size: 14px;
    color: $text-white;
    white-space: nowrap;
}

.phone-number {
    font-size: 30px; // 突出显示电话号码
    font-weight: bold;

}

// 响应式适配
@media (max-width: 1024px) {
    .info-items {
        gap: $spacing-lg;
    }

    .info-text {
        font-size: 13px;
    }
}

@media (max-width: 768px) {
    .info-content {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-md;
    }

    .info-items {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-sm;
        width: 100%;
    }

    .tabs-container {
        margin-left: $spacing-md;
    }
}
</style>