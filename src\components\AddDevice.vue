<template>
    <div class="modal-overlay">
        <div class="modal-container" @click.stop>
            <!-- 标题栏 -->
            <div class="modal-header">
                <div class="header-content">
                    <h2 class="modal-title">添加设备</h2>
                </div>
            </div>

            <!-- 表单内容 -->
            <div class="modal-body">
                <form class="device-form" @submit.prevent="handleSubmit">
                    <!-- 设备类型 -->
                    <div class="form-item">
                        <label class="form-label">设备类型</label>
                        <div class="form-control">
                            <select v-model="formData.deviceType" class="select-input">
                                <option value="">请选择设备类型</option>
                                <option value="monitor">监测设备</option>
                                <option value="camera">摄像设备</option>
                                <option value="sensor">传感器</option>
                            </select>
                            <svg class="dropdown-icon" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                        </div>
                    </div>

                    <!-- 设备编号 -->
                    <div class="form-item">
                        <label class="form-label">设备编号</label>
                        <div class="form-control">
                            <select v-model="formData.deviceNumber" class="select-input">
                                <option value="">请选择设备编号</option>
                                <option value="DEV001">DEV001</option>
                                <option value="DEV002">DEV002</option>
                                <option value="DEV003">DEV003</option>
                            </select>
                            <svg class="dropdown-icon" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                        </div>
                    </div>

                    <!-- 阈值设置 -->
                    <div class="form-item">
                        <label class="form-label">阈值设置</label>
                        <div class="form-control">
                            <input type="text" v-model="formData.threshold" class="text-input" placeholder="请输入" />
                        </div>
                    </div>

                    <!-- 绑定单位类型 -->
                    <div class="form-item">
                        <label class="form-label">绑定单位类型</label>
                        <div class="form-control">
                            <select v-model="formData.unitType" class="select-input">
                                <option value="">请选择单位类型</option>
                                <option value="hospital">医院</option>
                                <option value="clinic">诊所</option>
                                <option value="center">卫生中心</option>
                            </select>
                            <svg class="dropdown-icon" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                        </div>
                    </div>

                    <!-- 绑定单位名称 -->
                    <div class="form-item">
                        <label class="form-label">绑定单位名称</label>
                        <div class="form-control">
                            <select v-model="formData.unitName" class="select-input">
                                <option value="">请选择单位名称</option>
                                <option value="hospital1">漳平市和平镇卫生院</option>
                                <option value="clinic1">漳平市溪南镇南柄村第一卫生室</option>
                            </select>
                            <svg class="dropdown-icon" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 按钮区域 -->
            <div class="modal-footer">
                <button type="button" class="btn btn-cancel" @click="handleClose">关闭</button>
                <button type="button" class="btn btn-submit" @click="handleSubmit">提交</button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, defineEmits } from 'vue'

// Emits
const emit = defineEmits(['close', 'submit'])

// 表单数据
const formData = ref({
    deviceType: '',
    deviceNumber: '',
    threshold: '',
    unitType: '',
    unitName: ''
})

// 方法
const handleClose = () => {
    emit('close')
}

const handleOverlayClick = () => {
    emit('close')
}

const handleSubmit = () => {
    // 简单验证
    if (!formData.value.deviceType || !formData.value.deviceNumber) {
        alert('请填写必要信息')
        return
    }

    // 提交数据
    emit('submit', { ...formData.value })

    // 重置表单
    formData.value = {
        deviceType: '',
        deviceNumber: '',
        threshold: '',
        unitType: '',
        unitName: ''
    }
}
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;
@use "sass:color";

// 模态框遮罩
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    @include flex-center;
    z-index: 1000;
}

// 模态框容器
.modal-container {
    width: 1035px;
    height: 700px;
    max-width: 90vw;
    max-height: 90vh;
    box-shadow: $shadow-lg;
    overflow: hidden;
    background-color: rgba(12, 25, 44, 0.86);
}

// 标题栏
.modal-header {
    height: 57px;
    background: url(../assets/img/<EMAIL>) no-repeat;
    background-size: contain;
    background-position: left;
    @include flex-start;
    padding: 0 72px;
}

.header-content {
    @include flex-start;
    gap: $spacing-sm;
}

.arrow-icon {
    width: 20px; // 预留图标样式参数
    height: 20px;
    color: $text-white;
}

.modal-title {
    margin: 0;
    font-family: MicrosoftYaHei;
    font-size: 26px;
    color: #FFFFFF;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-weight: normal;
}

// 表单内容
.modal-body {
    padding: 100px 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.device-form {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
}

.form-item {
    display: flex;
    align-items: center;
    gap: $spacing-md;

}

.form-label {
    width: 180px; // 预留label宽度参数
    font-size: 20px; // 预留字号参数
    color: $text-white;
    text-align: right;
    flex-shrink: 0;
}

.form-control {
    position: relative;
    flex: 1;
    max-width: 700px;
}

// 下拉菜单样式
.select-input {
    width: 100%;
    height: $form-item-height; // 预留高度参数
    background: $blue-medium;
    color: $blue-light;
    border: none;
    padding: 0 $spacing-md;
    padding-right: 40px;
    font-size: 18px;
    cursor: pointer;
    appearance: none;
    outline: none;
    transition: all 0.3s ease;

    &:hover {
        background: color.adjust($blue-medium, $lightness: 5%, $space: hsl);
    }

    &:focus {
        background: color.adjust($blue-medium, $lightness: 8%, $space: hsl);
        box-shadow: 0 0 0 2px rgba(135, 206, 250, 0.3);
    }

    option {
        background: $blue-medium;
        color: $blue-light;
    }
}

// 输入框样式
.text-input {
    width: 100%;
    height: $form-item-height;
    background: $blue-medium;
    color: $blue-light;
    border: none;
    padding: 0 $spacing-md;
    font-size: 18px;
    outline: none;
    transition: all 0.3s ease;

    &::placeholder {
        color: $text-placeholder;
    }

    &:hover {
        background: color.adjust($blue-medium, $lightness: 5%, $space: hsl);
    }

    &:focus {
        background: color.adjust($blue-medium, $lightness: 8%, $space: hsl);
        box-shadow: 0 0 0 2px rgba(135, 206, 250, 0.3);
    }
}

// 下拉箭头图标
.dropdown-icon {
    position: absolute;
    right: $spacing-md;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    color: #999999;
    pointer-events: none;
}

// 按钮区域
.modal-footer {
    padding: $spacing-md $modal-padding;
    @include flex-center;
    gap: $spacing-md;
}

.btn {
    height: 41px; // 预留按钮高度参数
    padding: 0 $spacing-xl;
    border: none;
    border-radius: 21px 21px 21px 21px;
    font-size: 20px;
    font-weight: 500;
    line-height: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 226px;

    &:active {
        transform: translateY(1px); // 按压效果
    }
}

.btn-cancel {
    background: $blue-medium;
    color: $text-white;

    &:hover {
        background: color.adjust($blue-medium, $lightness: 10%, $space: hsl);
    }
}

.btn-submit {
    background: $blue-lighter;
    color: $text-white;

    &:hover {
        background: color.adjust($blue-medium, $lightness: 10%, $space: hsl);
    }
}

// 响应式适配
@media (max-width: 768px) {
    .modal-container {
        width: 95vw;
        margin: $spacing-md;
    }

    .form-item {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-sm;
    }

    .form-label {
        width: auto;
        text-align: left;
    }

    .form-control {
        width: 100%;
        max-width: none;
    }

    .modal-footer {
        flex-direction: column;

        .btn {
            width: 100%;
        }
    }
}
</style>